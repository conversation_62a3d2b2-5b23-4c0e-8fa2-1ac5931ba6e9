import numpy as np
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any, Union, Callable
from Bio import PDB
from Bio.PDB import <PERSON><PERSON><PERSON><PERSON><PERSON>, MMCIFParser
from Bio.SeqUtils import seq1
import warnings
from dataclasses import dataclass
from sklearn.neighbors import KD<PERSON>ree

from boltz.data.types import MinDistance
from boltz.data.parse.struct2seq import StructureSequence<PERSON><PERSON><PERSON>


def calculate_sequence_identity(
    aligned_seq1: str, aligned_seq2: str
    ) -> float:
    """
    Calculate sequence identity from aligned sequences.
    
    Parameters
    ----------
    aligned_seq1 : str
        First aligned sequence
    aligned_seq2 : str
        Second aligned sequence
        
    Returns
    -------
    float
        Sequence identity (0.0 to 1.0)
    """
    if len(aligned_seq1) != len(aligned_seq2):
        return 0.0
        
    matches = sum(1 for a, b in zip(aligned_seq1, aligned_seq2) 
        if a == b and a != '-' and b != '-'
    )
    aligned_length = sum(1 for a, b in zip(aligned_seq1, aligned_seq2) 
        if a != '-' and b != '-'
    )
    return matches / aligned_length if aligned_length > 0 else 0.0


@dataclass
class SequenceMappingResult:
    """
    Result of sequence mapping between template and query.
    
    Attributes
    ----------
    aligned_struct : str
        Aligned template structure sequence
    aligned_given : str
        Aligned query sequence
    mapping : List[Tuple[int, int]]
        Mapping from query indices to template indices
    seq_identity : float
        Sequence identity between aligned sequences
    mapping_dict : Dict[int, int]
        Dictionary version of mapping for fast lookup
    """
    aligned_struct: str
    aligned_given: str
    mapping: List[Tuple[int, int]]
    seq_identity: float
    mapping_dict: Dict[int, int]
    
    @classmethod
    def from_mapper_result(
        cls, 
        aligned_struct: str, 
        aligned_given: str, 
        mapping: List[Tuple[int, int]], 
        stats: Any
    ) -> 'SequenceMappingResult':
        """Create SequenceMappingResult from mapper output."""
        seq_identity = calculate_sequence_identity(aligned_struct, aligned_given)
        mapping_dict = dict(mapping)
        return cls(aligned_struct, aligned_given, mapping, seq_identity, mapping_dict)


class TemplateConstraintGenerator:
    """
    Template-based distance constraint generator for protein structure prediction.
    
    This class generates multiple distance constraints based on template structures
    to prevent non-physical conformations when using single atom pair constraints.
    """
    
    def __init__(
        self, 
        distance_threshold: float = 10.0,
        cb_distance_cutoff: float = 50.0,
        min_sequence_identity: float = 0.6,
        gap_penalty: float = -2.0,
        disulfide_distance_threshold: float = 2.2,
        include_disulfide: bool = True
    ):
        """
        Initialize the template constraint generator.
        
        Parameters
        ----------
        distance_threshold : float
            Maximum distance to consider for constraints (Angstroms)
        cb_distance_cutoff : float
            Maximum Cb-Cb distance for constraint generation (Angstroms)
        min_sequence_identity : float
            Minimum sequence identity for reliable alignment
        gap_penalty : float
            Gap penalty for sequence alignment
        disulfide_distance_threshold : float, default=2.2
            Maximum distance (Angstroms) to consider as disulfide bond
        include_disulfide : bool, default=True
            Whether to include disulfide bond constraints
        """
        self.min_sequence_identity      = min_sequence_identity
        # distance thresholds
        self.distance_threshold         = distance_threshold
        self.cb_distance_cutoff         = cb_distance_cutoff
        # disulfide bond
        self.include_disulfide          = include_disulfide
        self.disulfide_distance_threshold = disulfide_distance_threshold
        
        self.mapper                     = StructureSequenceMapper(gap_penalty=gap_penalty)
        
        print(f"  INFO: cb_distance_cutoff: {cb_distance_cutoff} (Angstroms)")
        if include_disulfide:
            print(f"  INFO: disulfide_distance_threshold: {disulfide_distance_threshold} (Angstroms)")
    
    def _extract_cb_coordinates(
        self, 
        structure_file: str, 
        chain_id: str
    ) -> Dict[int, np.ndarray]:
        """
        Extract Cb coordinates from template structure.
        
        Parameters
        ----------
        structure_file : str
            Path to template structure file
        chain_id : str
            Chain identifier
            
        Returns
        -------
        Dict[int, np.ndarray]
            Dictionary mapping residue index to Cb coordinates
        """
        try:
            structure = self.mapper._get_structure_parser(structure_file)
            cb_coords = {}
            
            for model in structure:
                for chain in model:
                    if chain.id == chain_id:
                        residue_idx = 0
                        for residue in chain:
                            if residue.id[0] == " ":  # Standard residue                                
                                res_name = residue.resname.strip() # Get residue name
                                
                                # Skip GLY and PRO residues for constraint generation
                                if res_name in ["GLY", "PRO"]:
                                    # Skip these residues - no constraints will be generated
                                    pass
                                # if res_name not in ["GLY", "PRO"], try to get CB atom
                                else:                            
                                    cb_atom = None
                                    if 'CB' in residue: # For other residues, try CB first
                                        cb_atom = residue['CB']                                    
                                    else:
                                        warnings.warn(f"Missing CB atom in {res_name} residue at position {residue_idx}")
                                    
                                    if cb_atom is not None:
                                        cb_coords[residue_idx] = np.array(cb_atom.get_coord())
                                    else:
                                        warnings.warn(f"Missing CB atom in {res_name}{residue_idx} residue")

                                residue_idx += 1
                        break
                break
                        
            return cb_coords
            
        except Exception as e:
            warnings.warn(f"Failed to extract Cb coordinates: {e}")
            return {}
    
    def _extract_sulfur_coordinates(
        self, 
        structure_file: str, 
        chain_id: str
    ) -> Dict[int, np.ndarray]:
        """
        Extract SG coordinates from CYS residues for disulfide bond detection.
        
        Parameters
        ----------
        structure_file : str
            Path to template structure file
        chain_id : str
            Chain identifier
            
        Returns
        -------
        Dict[int, np.ndarray]
            Dictionary mapping residue index to SG coordinates for CYS residues only
        """
        try:
            structure = self.mapper._get_structure_parser(structure_file)
            sg_coords = {}
            
            for model in structure:
                for chain in model:
                    if chain.id == chain_id:
                        residue_idx = 0
                        for residue in chain:
                            if residue.id[0] == " ":  # Standard residue
                                res_name = residue.resname.strip()
                                
                                # Only process CYS residues
                                if res_name == "CYS":
                                    if 'SG' in residue:
                                        sg_atom = residue['SG']
                                        sg_coords[residue_idx] = np.array(sg_atom.get_coord())
                                    else:
                                        warnings.warn(f"Missing SG atom in CYS residue at position {residue_idx}")
                                
                                residue_idx += 1
                        break
                break
                        
            return sg_coords
            
        except Exception as e:
            warnings.warn(f"Failed to extract SG coordinates: {e}")
            return {}
    
    def _compute_distance_pairs(
        self, 
        coords: Dict[int, np.ndarray],
        distance_filter: Callable[[float], bool],
        return_format: str = "map"
    ) -> Union[Dict[Tuple[int, int], float], List[Tuple[int, int, float]]]:
        """
        Compute distance pairs from coordinates with filtering.
        
        Parameters
        ----------
        coords : Dict[int, np.ndarray]
            Dictionary mapping residue index to coordinates
        distance_filter : Callable[[float], bool]
            Function to filter distances (returns True if distance should be included)
        return_format : str, default="map"
            Return format: "map" for dict, "bonds" for list
            
        Returns
        -------
        Union[Dict[Tuple[int, int], float], List[Tuple[int, int, float]]]
            Distance pairs in requested format
        """
        results = []
        residue_indices = list(coords.keys())
        
        for i, idx1 in enumerate(residue_indices):
            for idx2 in residue_indices[i+1:]:  # Avoid duplicate pairs
                if idx1 in coords and idx2 in coords:
                    coord1 = coords[idx1]
                    coord2 = coords[idx2]
                    distance = np.linalg.norm(coord1 - coord2)
                    
                    if distance_filter(distance):
                        results.append((idx1, idx2, distance))
        
        if return_format == "map":
            return {(idx1, idx2): distance for idx1, idx2, distance in results}
        elif return_format == "bonds":
            return results
        else:
            raise ValueError(f"Unknown return format: {return_format}")
    

    def _prepare_sequence_mapping(
        self,
        template_structure: str,
        template_chain_id: str,
        query_sequence: str
    ) -> Optional[SequenceMappingResult]:
        """
        Prepare sequence mapping between template and query sequences.
        
        Parameters
        ----------
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_sequence : str
            Query protein sequence
            
        Returns
        -------
        Optional[SequenceMappingResult]
            Sequence mapping result, or None if mapping fails or identity is too low
        """
        try:
            # Map sequences
            aligned_struct, aligned_given, mapping, stats = self.mapper.map_sequences(
                template_structure, template_chain_id, query_sequence
            )
            
            # Create mapping result
            mapping_result = SequenceMappingResult.from_mapper_result(
                aligned_struct, aligned_given, mapping, stats
            )
            
            print(f"  INFO: Sequence identity: {mapping_result.seq_identity:.3f}")
            
            # Check sequence identity threshold
            if mapping_result.seq_identity < self.min_sequence_identity:
                warnings.warn(
                    f"Low sequence identity ({mapping_result.seq_identity:.3f}) "
                    f"may lead to unreliable constraints"
                )
                return None
            
            return mapping_result
            
        except Exception as e:
            warnings.warn(f"Failed to prepare sequence mapping: {e}")
            return None
    
    def _create_constraint_from_mapping(
        self,
        template_idx1: int,
        template_idx2: int,
        distance: float,
        mapping_result: SequenceMappingResult,
        query_sequence: str,
        query_chain_id: str,
        constraint_type: str,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool,
        atom1_name: str = None,
        atom2_name: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        Create constraint from template mapping information.
        
        Parameters
        ----------
        template_idx1, template_idx2 : int
            Template residue indices
        distance : float
            Distance between atoms/residues
        mapping_result : SequenceMappingResult
            Sequence mapping information
        query_sequence : str
            Query protein sequence
        query_chain_id : str
            Query chain identifier
        constraint_type : str
            Type of constraint ("min_distance", "nmr_distance", "bond")
        distance_buffer : float
            Distance buffer for bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
        atom1_name, atom2_name : str, optional
            Specific atom names (if None, will determine based on residue type)
            
        Returns
        -------
        Optional[Dict[str, Any]]
            Constraint dictionary, or None if mapping fails
        """
        # Find corresponding query indices
        query_idx1 = None
        query_idx2 = None
        
        for query_idx, template_idx in mapping_result.mapping_dict.items():
            if template_idx == template_idx1:
                query_idx1 = query_idx
            elif template_idx == template_idx2:
                query_idx2 = query_idx
        
        if query_idx1 is None or query_idx2 is None:
            return None
        
        atom1_name = "CB"
        atom2_name = "CB"
        
        # Generate constraint based on type
        if constraint_type == "min_distance":
            constraint = {
                "min_distance": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name],  # 1-indexed
                    "distance": float(distance)
                }
            }
        elif constraint_type == "nmr_distance":
            if distance <= 10.0: # Calculate bounds with buffer
                lower_bound = distance * (1 - distance_buffer)
                upper_bound = distance * (1 + distance_buffer)
            else:
                lower_bound = distance - 1.0
                upper_bound = distance * (1 + distance_buffer)
            
            # Calculate weight
            weight = base_weight
            if sequence_identity_weight:
                weight *= mapping_result.seq_identity
            
            constraint = {
                "nmr_distance": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name],  # 1-indexed
                    "lower_bound": round(float(lower_bound), 3),
                    "upper_bound": round(float(upper_bound), 3),
                    "weight": float(weight)
                }
            }
        elif constraint_type == "bond":
            constraint = {
                "bond": {
                    "atom1": [query_chain_id, query_idx1 + 1, atom1_name],  # 1-indexed
                    "atom2": [query_chain_id, query_idx2 + 1, atom2_name]   # 1-indexed
                }
            }
        else:
            raise ValueError(f"Unknown constraint type: {constraint_type}")
        
        return constraint
    
    def generate_cb_cb_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str,
        constraint_type: str,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool
    ) -> List[Dict[str, Any]]:
        """
        Generate Cb-Cb distance constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier
        constraint_type : str
            Type of constraint ("min_distance" or "nmr_distance")
        distance_buffer : float
            Distance buffer for NMR bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
            
        Returns
        -------
        List[Dict[str, Any]]
            List of Cb-Cb constraint dictionaries
        """
        # Prepare sequence mapping
        mapping_result = self._prepare_sequence_mapping(
            template_structure, template_chain_id, query_sequence
        )
        if mapping_result is None:
            return []
        
        # Extract CB coordinates from template
        cb_coords = self._extract_cb_coordinates(template_structure, template_chain_id)
        if not cb_coords: 
            print(f"  INFO: No Cb atoms found in the given template")
            return []
        
        # Compute distance map using generic method with Cb-specific filter        
        distance_filter = lambda d: 0 <= d <= 10000 # 1. constraint가 너무 많아서 그런지 diffusion 되다만 구조
        # distance_filter = lambda d: 0 <= d <= 50 # 2. 전반적으로 되긴 하지만 아직 별로...? 
        # distance_filter = lambda d: 10 <= d <= 10000 # 3. constraint가 너무 많아서 그런지 diffusion 되다만 구조
        # distance_filter = lambda d: 10 <= d <= 50 # 4. 전반적으로 되긴 하지만, 여전히 C-terminal 부분 잘 안 됨
        # distance_filter = lambda d: 10 <= d <= 20 # 5. closed form 예측
        distance_map = self._compute_distance_pairs(cb_coords, distance_filter, return_format="map")
        if not distance_map:
            return []
        
        # Generate constraints based on mapping
        constraints = []
        for (template_idx1, template_idx2), distance in distance_map.items():
            constraint = self._create_constraint_from_mapping(
                template_idx1=template_idx1,
                template_idx2=template_idx2,
                distance=distance,
                mapping_result=mapping_result,
                query_sequence=query_sequence,
                query_chain_id=query_chain_id,
                constraint_type=constraint_type,
                distance_buffer=distance_buffer,
                base_weight=base_weight,
                sequence_identity_weight=sequence_identity_weight
            )
            
            if constraint is not None:
                constraints.append(constraint)
        
        return constraints
    
    def generate_disulfide_constraints_internal(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str,
        disulfide_distance_threshold: float,
        distance_buffer: float,
        base_weight: float,
        sequence_identity_weight: bool,
        constraint_type: str
    ) -> List[Dict[str, Any]]:
        """
        Internal method to generate disulfide constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier
        disulfide_distance_threshold : float
            Maximum distance to consider as disulfide bond
        distance_buffer : float
            Distance buffer for NMR bounds
        base_weight : float
            Base weight for constraints
        sequence_identity_weight : bool
            Whether to apply sequence identity weighting
        constraint_type : str
            Type of constraint ("nmr_distance", "bond", or "both")
            
        Returns
        -------
        List[Dict[str, Any]]
            List of disulfide constraint dictionaries
        """
        # Prepare sequence mapping
        mapping_result = self._prepare_sequence_mapping(
            template_structure, template_chain_id, query_sequence
        )
        if mapping_result is None:
            return []
        
        # Extract SG coordinates from template
        sg_coords = self._extract_sulfur_coordinates(template_structure, template_chain_id)
        if not sg_coords:
            print(f"  INFO: No CYS residues found in template")
            return []
        
        # Find disulfide bonds using generic method with disulfide-specific filter
        distance_filter = lambda d: d <= disulfide_distance_threshold
        disulfide_bonds = self._compute_distance_pairs(sg_coords, distance_filter, return_format="bonds")
        if not disulfide_bonds:
            print(f"  INFO: No disulfide bonds found in template")
            return []
        
        print(f"  INFO: Found {len(disulfide_bonds)} disulfide bonds in template")
        
        # Generate constraints based on mapping
        constraints = []
        for template_idx1, template_idx2, distance in disulfide_bonds:
            # Check if both residues are CYS in query sequence
            query_idx1 = mapping_result.mapping_dict.get(template_idx1)
            query_idx2 = mapping_result.mapping_dict.get(template_idx2)
            
            if (query_idx1 is not None and query_idx2 is not None and 
                    query_idx1 < len(query_sequence) and query_idx2 < len(query_sequence) and
                    query_sequence[query_idx1] == 'C' and query_sequence[query_idx2] == 'C'
                ):
                # Generate constraints based on type
                constraint_types = []
                if constraint_type in ["nmr_distance", "both"]:
                    constraint_types.append("nmr_distance")
                if constraint_type in ["bond", "both"]:
                    constraint_types.append("bond")
                
                for c_type in constraint_types:
                    if c_type == "nmr_distance":
                        # Disulfide specific bounds, the minimum distance between two CYS residues for S-S bond
                        lower_bound = 2.02 
                        upper_bound = distance * (1 + distance_buffer)
                        
                        weight = base_weight
                        if sequence_identity_weight:
                            weight *= mapping_result.seq_identity
                        
                        constraint = {
                            "nmr_distance": {
                                "atom1": [query_chain_id, query_idx1 + 1, "SG"], # 1-indexed
                                "atom2": [query_chain_id, query_idx2 + 1, "SG"], # 1-indexed  
                                "lower_bound": round(float(lower_bound), 3),    
                                "upper_bound": round(float(upper_bound), 3),
                                "weight": float(weight)
                            }
                        }
                        constraints.append(constraint)
                        
                        print(f"    Generated S-S NMRDistanceConstraint: S{query_idx1+1}-S{query_idx2+1} "
                            f"({lower_bound:.2f}-{upper_bound:.2f}Å, weight={weight:.3f})"
                        )
                    # elif c_type == "bond": [NOTE] 우선 muted
                    #     constraint = {
                    #         "bond": {
                    #             "atom1": [query_chain_id, query_idx1 + 1, "SG"],  # 1-indexed
                    #             "atom2": [query_chain_id, query_idx2 + 1, "SG"]   # 1-indexed
                    #         }
                    #     }
                    #     constraints.append(constraint)
                    #     print(f"    Generated S-S covalent bond constraint: S{query_idx1+1}-S{query_idx2+1}")
        
        return constraints
    
    def generate_template_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str = "A",
        constraint_type: str = "nmr_distance",
        distance_buffer: float = 0.1,
        base_weight: float = 1.0,
        cb_cb_weight: Optional[float] = None,
        disulfide_weight: Optional[float] = None,
        sequence_identity_weight: bool = True,
        # include_disulfide: Optional[bool] = None,
        # include_disulfide_nmr: bool = True,
        # include_disulfide_bond: bool = True
        include_disulfide: Optional[bool] = False, # [TODO] (6/24) 나중에 전반적인 conformation이 맞으면 이후 refine
        include_disulfide_nmr: bool = False,
        include_disulfide_bond: bool = False    
    ) -> List[Dict[str, Any]]:
        """
        Generate template-based distance constraints including Cb-Cb and optionally S-S constraints.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier (default: "A")
        constraint_type : str
            Type of constraint to generate ("min_distance" or "nmr_distance", default: "nmr_distance")
        distance_buffer : float
            Buffer percentage for NMR bounds (default: 0.1 = 10%)
        base_weight : float
            Base weight for NMR constraints (default: 1.0, used as fallback)
        cb_cb_weight : Optional[float]
            Specific weight for Cb-Cb distance constraints (default: None, uses base_weight or 1.0)
        disulfide_weight : Optional[float]
            Specific weight for disulfide constraints (default: None, uses base_weight or 2.0)
        sequence_identity_weight : bool
            Whether to scale weight by sequence identity (default: True)
        include_disulfide : Optional[bool]
            Whether to include disulfide constraints (default: use class setting, deprecated)
        include_disulfide_nmr : bool, default=True
            Whether to include disulfide NMR distance constraints
        include_disulfide_bond : bool, default=True
            Whether to include disulfide bond constraints
            
        Returns
        -------
        List[Dict[str, Any]]
            List of constraint dictionaries compatible with boltz schema
        """
        try:
            # Use class setting if not explicitly provided (backward compatibility)
            if include_disulfide is None:
                include_disulfide = self.include_disulfide
            
            # Set specific weights with fallback to base_weight or defaults
            cb_cb_weight = cb_cb_weight if cb_cb_weight is not None else base_weight if base_weight != 1.0 else 1.0
            disulfide_weight = disulfide_weight if disulfide_weight is not None else 2.0
            
            print(f"  INFO: Using weights - Cb-Cb: {cb_cb_weight}, Disulfide: {disulfide_weight}")
            
            # Generate Cb-Cb constraints
            constraints = self.generate_cb_cb_constraints(
                query_sequence=query_sequence,
                template_structure=template_structure,
                template_chain_id=template_chain_id,
                query_chain_id=query_chain_id,
                constraint_type=constraint_type,
                distance_buffer=distance_buffer,
                base_weight=cb_cb_weight,
                sequence_identity_weight=sequence_identity_weight
            )
            
            cb_count = len(constraints)
            print(f"  INFO: Generated {cb_count} Cb-Cb template constraints")
            
            # Generate disulfide constraints if enabled (backward compatibility)
            if include_disulfide and (include_disulfide_nmr or include_disulfide_bond):
                # Determine constraint type for disulfide bonds
                disulfide_constraint_type = []
                if include_disulfide_nmr:
                    disulfide_constraint_type.append("nmr_distance")
                if include_disulfide_bond:
                    disulfide_constraint_type.append("bond")
                
                # Generate constraints for each type
                for d_constraint_type in disulfide_constraint_type:
                    disulfide_constraints = self.generate_disulfide_constraints_internal(
                        query_sequence=query_sequence,
                        template_structure=template_structure,
                        template_chain_id=template_chain_id,
                        query_chain_id=query_chain_id,
                        disulfide_distance_threshold=self.disulfide_distance_threshold,
                        distance_buffer=distance_buffer,
                        base_weight=disulfide_weight,
                        sequence_identity_weight=sequence_identity_weight,
                        constraint_type=d_constraint_type
                    )
                    constraints.extend(disulfide_constraints)
                
                print(f"  INFO: Added disulfide constraints (NMR: {include_disulfide_nmr}, Bond: {include_disulfide_bond})")
            
            print(f"  INFO: Total template constraints generated: {len(constraints)}")
            return constraints
            
        except Exception as e:
            warnings.warn(f"Failed to generate template constraints: {e}")
            return []
    
    def generate_constraints_for_boltz_schema(
        self,
        schema_data: Dict[str, Any],
        template_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate constraints and integrate into boltz schema.
        
        Parameters
        ----------
        schema_data : Dict[str, Any]
            Original boltz schema data
        template_info : Dict[str, Any]
            Template information
            
        Returns
        -------
        Dict[str, Any]
            Schema data with added template constraints
        """
        try:
            # Extract template constraints
            constraints = self.generate_template_constraints(**template_info)
            
            # Add to schema
            if "constraints" not in schema_data:
                schema_data["constraints"] = []
            
            schema_data["constraints"].extend(constraints)
            
            return schema_data
            
        except Exception as e:
            warnings.warn(f"Failed to integrate template constraints: {e}")
            return schema_data

    def generate_disulfide_constraints(
        self,
        query_sequence: str,
        template_structure: str,
        template_chain_id: str,
        query_chain_id: str = "A",
        disulfide_distance_threshold: float = 2.2,
        distance_buffer: float = 0.01,
        base_weight: float = 2.0,
        sequence_identity_weight: bool = True,
        constraint_type: str = "both"
    ) -> List[Dict[str, Any]]:
        """
        Generate disulfide bond constraints from template structure.
        
        Parameters
        ----------
        query_sequence : str
            Query protein sequence
        template_structure : str
            Path to template structure file
        template_chain_id : str
            Template chain identifier
        query_chain_id : str
            Query chain identifier (default: "A")
        disulfide_distance_threshold : float, default=2.2
            Maximum distance (Angstroms) to consider as disulfide bond
        distance_buffer : float, default=0.1
            Buffer percentage for NMR bounds (default: 0.1 = 10%)
        base_weight : float, default=1.0
            Base weight for NMR constraints
        sequence_identity_weight : bool, default=True
            Whether to scale weight by sequence identity
        constraint_type : str, default="nmr_distance"
            Type of constraint to generate ("nmr_distance", "bond", or "both")
            
        Returns
        -------
        List[Dict[str, Any]]
            List of disulfide constraint dictionaries in boltz schema format
        """
        try:
            # Use the internal method for actual constraint generation
            constraints = self.generate_disulfide_constraints_internal(
                query_sequence=query_sequence,
                template_structure=template_structure,
                template_chain_id=template_chain_id,
                query_chain_id=query_chain_id,
                disulfide_distance_threshold=disulfide_distance_threshold,
                distance_buffer=distance_buffer,
                base_weight=base_weight,
                sequence_identity_weight=sequence_identity_weight,
                constraint_type=constraint_type
            )
            
            constraint_type_str = constraint_type.replace("_", " ").title()
            print(f"  INFO: Generated {len(constraints)} disulfide {constraint_type_str} constraints")
            return constraints
            
        except Exception as e:
            # A3 answer: Warning and continue
            warnings.warn(f"Failed to generate disulfide constraints: {e}")
            return []


class MultichainTemplateConstraintGenerator(TemplateConstraintGenerator):
    """
    Extended template constraint generator supporting multichain complexes.
    
    Key enhancements:
    - Cross-chain distance constraints
    - Interface-based constraint generation  
    - Multi-template integration
    - Optimized chain mapping
    """
    
    def __init__(
        self,
        interface_distance_cutoff: float=4.0,
        cross_chain_weight_factor: float=1.0,
        **kwargs
    ):
        """
        Initialize the multichain template constraint generator.
        
        Parameters
        ----------
        interface_distance_cutoff : float, default=6.0
            Maximum distance (Angstroms) to consider as interface contact
        cross_chain_weight_factor : float, default=1.0
            Weight factor for cross-chain constraints
        **kwargs
            Additional arguments passed to parent class
        """
        super().__init__(**kwargs)
        self.interface_distance_cutoff = interface_distance_cutoff
        self.cross_chain_weight_factor = cross_chain_weight_factor
        
        print(f"  INFO: interface_distance_cutoff: {interface_distance_cutoff} (Angstroms)")
        print(f"  INFO: cross_chain_weight_factor: {cross_chain_weight_factor}")
    
    def _extract_interface_residues(
        self,
        structure_file: str,
        chain1_id: str,
        chain2_id: str,
        cutoff: float = None
    ) -> List[Tuple[int, int, float]]:
        """
        Extract interface residue pairs between two template chains.
        
        Parameters
        ----------
        structure_file : str
            Path to template structure file
        chain1_id : str
            First chain identifier
        chain2_id : str
            Second chain identifier  
        cutoff : float, optional
            Interface distance cutoff (uses class default if None)
            
        Returns
        -------
        List[Tuple[int, int, float]]
            List of (chain1_res_idx, chain2_res_idx, distance) tuples
        """
        if cutoff is None:
            cutoff = self.interface_distance_cutoff
            
        try:
            # Get coordinates for both chains
            coords1 = self._extract_cb_coordinates(structure_file, chain1_id)
            coords2 = self._extract_cb_coordinates(structure_file, chain2_id)
            
            if not coords1 or not coords2:
                return []
            
            # Compute all pairwise distances between chains
            interface_pairs = []
            
            for res1_idx, coord1 in coords1.items():
                for res2_idx, coord2 in coords2.items():
                    distance = np.linalg.norm(coord1 - coord2)
                    
                    if distance <= cutoff:
                        interface_pairs.append((res1_idx, res2_idx, distance))
            
            print(f"  INFO: Found {len(interface_pairs)} interface contacts between {chain1_id} and {chain2_id}")
            return interface_pairs
            
        except Exception as e:
            warnings.warn(f"Failed to extract interface residues: {e}")
            return []
    
    def generate_cross_chain_constraints(
        self,
        chain1_id: str,
        chain1_sequence: str,
        chain2_id: str, 
        chain2_sequence: str,
        template_structure: str,
        template_chain1_id: str,
        template_chain2_id: str,
        constraint_type: str = "nmr_distance",
        distance_buffer: float = 0.1,
        base_weight: float = 1.0,
        sequence_identity_weight: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Generate cross-chain distance constraints between two chains.
        
        Parameters
        ----------
        chain1_id : str
            First query chain identifier
        chain1_sequence : str
            First query chain sequence
        chain2_id : str
            Second query chain identifier
        chain2_sequence : str
            Second query chain sequence
        template_structure : str
            Path to template structure file
        template_chain1_id : str
            First template chain identifier
        template_chain2_id : str
            Second template chain identifier
        constraint_type : str, default="nmr_distance"
            Type of constraint to generate
        distance_buffer : float, default=0.1
            Buffer percentage for bounds
        base_weight : float, default=1.0
            Base weight for constraints
        sequence_identity_weight : bool, default=True
            Whether to apply sequence identity weighting
            
        Returns
        -------
        List[Dict[str, Any]]
            List of cross-chain constraint dictionaries
        """
        try:
            # Prepare sequence mappings for both chains
            mapping1 = self._prepare_sequence_mapping(
                template_structure, template_chain1_id, chain1_sequence
            )
            mapping2 = self._prepare_sequence_mapping(
                template_structure, template_chain2_id, chain2_sequence
            )
            
            if mapping1 is None or mapping2 is None:
                return []
            
            # Extract interface residue pairs
            interface_pairs = self._extract_interface_residues(
                template_structure, template_chain1_id, template_chain2_id
            )
            
            if not interface_pairs:
                return []
            
            # Generate constraints for interface pairs
            constraints = []
            
            for template_idx1, template_idx2, distance in interface_pairs:
                # Map template residues to query residues
                query_idx1 = mapping1.mapping_dict.get(template_idx1)
                query_idx2 = mapping2.mapping_dict.get(template_idx2)
                
                if query_idx1 is None or query_idx2 is None:
                    continue
                
                # Determine atom names
                atom1_name = "CB"
                atom2_name = "CB"
                
                # Calculate weight (average of both chain sequence identities)
                avg_seq_identity = (mapping1.seq_identity + mapping2.seq_identity) / 2.0
                weight = base_weight * self.cross_chain_weight_factor
                
                if sequence_identity_weight:
                    weight *= avg_seq_identity
                
                # Generate constraint
                if constraint_type == "nmr_distance":
                    lower_bound = max(0.0, distance * (1 - distance_buffer))
                    upper_bound = distance * (1 + distance_buffer)
                    
                    constraint = {
                        "nmr_distance": {
                            "atom1": [chain1_id, query_idx1 + 1, atom1_name],  # 1-indexed
                            "atom2": [chain2_id, query_idx2 + 1, atom2_name],  # 1-indexed
                            "lower_bound": float(lower_bound),
                            "upper_bound": float(upper_bound),
                            "weight": float(weight)
                        }
                    }
                    constraints.append(constraint)
                    
                elif constraint_type == "bond":
                    constraint = {
                        "bond": {
                            "atom1": [chain1_id, query_idx1 + 1, atom1_name],  # 1-indexed
                            "atom2": [chain2_id, query_idx2 + 1, atom2_name]   # 1-indexed
                        }
                    }
                    constraints.append(constraint)
            
            print(f"  INFO: Generated {len(constraints)} cross-chain constraints between {chain1_id} and {chain2_id}")
            return constraints
            
        except Exception as e:
            warnings.warn(f"Failed to generate cross-chain constraints: {e}")
            return []
    
    def generate_multichain_constraints(
        self,
        chain_sequences: Dict[str, str],
        template_structure: str,
        template_chain_mapping: Dict[str, str],
        constraint_type: str = "nmr_distance",
        distance_buffer: float = 0.1,
        base_weight: float = 1.0,
        cb_cb_weight: Optional[float] = None,
        disulfide_weight: Optional[float] = None,
        sequence_identity_weight: bool = True,
        include_intra_chain: bool = True,
        include_cross_chain: bool = True,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Generate constraints for multiple chains simultaneously.
        
        Parameters
        ----------
        chain_sequences : Dict[str, str]
            Dictionary mapping chain IDs to sequences
        template_structure : str
            Path to template structure file
        template_chain_mapping : Dict[str, str]
            Dictionary mapping query chain IDs to template chain IDs
        constraint_type : str, default="nmr_distance"
            Type of constraint to generate
        distance_buffer : float, default=0.1
            Buffer percentage for bounds
        base_weight : float, default=1.0
            Base weight for constraints (fallback)
        cb_cb_weight : Optional[float]
            Specific weight for Cb-Cb distance constraints (default: None, uses 1.0)
        disulfide_weight : Optional[float]
            Specific weight for disulfide constraints (default: None, uses 2.0)
        sequence_identity_weight : bool, default=True
            Whether to apply sequence identity weighting
        include_intra_chain : bool, default=True
            Whether to include intra-chain constraints
        include_cross_chain : bool, default=True
            Whether to include cross-chain constraints
        **kwargs
            Additional arguments
            
        Returns
        -------
        List[Dict[str, Any]]
            List of all constraint dictionaries
        """
        all_constraints = []
        
        # Generate intra-chain constraints for each chain
        if include_intra_chain:
            for chain_id, sequence in chain_sequences.items():
                if chain_id in template_chain_mapping:
                    template_chain_id = template_chain_mapping[chain_id]
                    
                    # Use parent class method for intra-chain constraints
                    intra_constraints = self.generate_template_constraints(
                        query_sequence=sequence,
                        template_structure=template_structure,
                        template_chain_id=template_chain_id,
                        query_chain_id=chain_id,
                        constraint_type=constraint_type,
                        distance_buffer=distance_buffer,
                        base_weight=base_weight,
                        cb_cb_weight=cb_cb_weight,
                        disulfide_weight=disulfide_weight,
                        sequence_identity_weight=sequence_identity_weight,
                        **kwargs
                    )
                    all_constraints.extend(intra_constraints)
        
        # Generate cross-chain constraints for all chain pairs
        if include_cross_chain:
            chain_ids = list(chain_sequences.keys())
            
            for i, chain1_id in enumerate(chain_ids):
                for chain2_id in chain_ids[i+1:]:  # Avoid duplicate pairs
                    if chain1_id in template_chain_mapping and chain2_id in template_chain_mapping:
                        template_chain1_id = template_chain_mapping[chain1_id]
                        template_chain2_id = template_chain_mapping[chain2_id]
                        
                        cross_constraints = self.generate_cross_chain_constraints(
                            chain1_id=chain1_id,
                            chain1_sequence=chain_sequences[chain1_id],
                            chain2_id=chain2_id,
                            chain2_sequence=chain_sequences[chain2_id],
                            template_structure=template_structure,
                            template_chain1_id=template_chain1_id,
                            template_chain2_id=template_chain2_id,
                            constraint_type=constraint_type,
                            distance_buffer=distance_buffer,
                            base_weight=base_weight,
                            sequence_identity_weight=sequence_identity_weight
                        )
                        all_constraints.extend(cross_constraints)
        
        intra_count = len([c for c in all_constraints 
                          if (c.get("nmr_distance", {}).get("atom1", [None])[0] == 
                              c.get("nmr_distance", {}).get("atom2", [None])[0]) or
                             (c.get("bond", {}).get("atom1", [None])[0] == 
                              c.get("bond", {}).get("atom2", [None])[0])])
        cross_count = len(all_constraints) - intra_count
        
        print(f"  INFO: Generated {len(all_constraints)} total constraints:")
        print(f"    Intra-chain: {intra_count}")
        print(f"    Cross-chain: {cross_count}")
        
        return all_constraints
    
    def resolve_multi_template_conflicts(
        self,
        constraint_groups: List[List[Dict[str, Any]]],
        resolution_strategy: str = "min_max_buffer"
    ) -> List[Dict[str, Any]]:
        """
        Resolve conflicts when using multiple templates.
        
        Parameters
        ----------
        constraint_groups : List[List[Dict[str, Any]]]
            List of constraint lists from different templates
        resolution_strategy : str, default="min_max_buffer"
            Strategy for conflict resolution
            
        Returns
        -------
        List[Dict[str, Any]]
            Resolved constraint list
        """
        if not constraint_groups:
            return []
        
        if len(constraint_groups) == 1:
            return constraint_groups[0]
        
        # Group constraints by atom pairs
        constraint_map = {}
        
        for constraints in constraint_groups:
            for constraint in constraints:
                # Extract constraint key (atom pair)
                key = None
                constraint_data = None
                
                if "nmr_distance" in constraint:
                    data = constraint["nmr_distance"]
                    atom1 = tuple(data["atom1"])
                    atom2 = tuple(data["atom2"])
                    key = (min(atom1, atom2), max(atom1, atom2))
                    constraint_data = data
                elif "bond" in constraint:
                    data = constraint["bond"]
                    atom1 = tuple(data["atom1"])
                    atom2 = tuple(data["atom2"])
                    key = (min(atom1, atom2), max(atom1, atom2))
                    constraint_data = data
                
                if key and constraint_data:
                    if key not in constraint_map:
                        constraint_map[key] = []
                    constraint_map[key].append((constraint, constraint_data))
        
        # Resolve conflicts
        resolved_constraints = []
        
        for key, constraint_list in constraint_map.items():
            if len(constraint_list) == 1:
                # No conflict
                resolved_constraints.append(constraint_list[0][0])
            else:
                # Resolve conflict
                if resolution_strategy == "min_max_buffer":
                    resolved = self._resolve_min_max_buffer(constraint_list)
                else:
                    # Default: take first constraint
                    resolved = constraint_list[0][0]
                
                if resolved:
                    resolved_constraints.append(resolved)
        
        print(f"  INFO: Resolved {len(constraint_map)} constraint groups to {len(resolved_constraints)} constraints")
        return resolved_constraints
    
    def _resolve_min_max_buffer(
        self,
        constraint_list: List[Tuple[Dict[str, Any], Dict[str, Any]]]
    ) -> Optional[Dict[str, Any]]:
        """
        Resolve conflicts using min-max buffer strategy.
        
        Parameters
        ----------
        constraint_list : List[Tuple[Dict[str, Any], Dict[str, Any]]]
            List of (constraint_dict, constraint_data) tuples
            
        Returns
        -------
        Optional[Dict[str, Any]]
            Resolved constraint dictionary, or None if resolution fails
        """
        # Only handle NMR distance constraints for now
        nmr_constraints = [(c, d) for c, d in constraint_list if "nmr_distance" in c]
        
        if not nmr_constraints:
            return constraint_list[0][0]  # Return first if no NMR constraints
        
        # Extract bounds and weights
        lower_bounds = []
        upper_bounds = []
        weights = []
        
        for constraint, data in nmr_constraints:
            lower_bounds.append(data.get("lower_bound", 0.0))
            upper_bounds.append(data.get("upper_bound", float('inf')))
            weights.append(data.get("weight", 1.0))
        
        # Calculate min-max range with buffer
        min_lower = min(lower_bounds)
        max_upper = max([ub for ub in upper_bounds if ub != float('inf')])
        
        if max_upper == float('inf'):
            max_upper = max(lower_bounds) * 2.0  # Fallback
        
        # Apply buffer (10% expansion)
        buffer = 0.1
        final_lower = max(0.0, min_lower * (1 - buffer))
        final_upper = max_upper * (1 + buffer)
        
        # Use average weight
        final_weight = sum(weights) / len(weights)
        
        # Create resolved constraint
        template_constraint = nmr_constraints[0][0]
        template_data = nmr_constraints[0][1]
        
        resolved_constraint = {
            "nmr_distance": {
                "atom1": template_data["atom1"],
                "atom2": template_data["atom2"],
                "lower_bound": float(final_lower),
                "upper_bound": float(final_upper),
                "weight": float(final_weight)
            }
        }
        
        return resolved_constraint
