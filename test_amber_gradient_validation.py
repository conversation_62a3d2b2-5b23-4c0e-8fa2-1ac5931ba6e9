#!/usr/bin/env python3
"""
Test script for Amber force field gradient validation.

This script tests the numerical vs analytic gradient validation functionality
for AmberBondPotential, AmberAnglePotential, and AmberDihedralPotential.
"""

import torch
import numpy as np
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from boltz.model.potentials.potentials import (
    AmberBondPotential, 
    AmberAnglePotential, 
    AmberDihedralPotential
)


def create_test_amber_bond_data():
    """Create test data for Amber bond potential validation."""
    print("Creating test data for Amber bond potential...")

    # Create simple test coordinates (2 atoms for 1 bond)
    coords = torch.tensor([
        [[0.0, 0.0, 0.0],   # Atom 0
         [1.5, 0.0, 0.0]]   # Atom 1
    ], dtype=torch.float32).unsqueeze(0)  # Add batch dimension

    # Create features for 1 bond: 0-1
    # Based on the actual compute_args implementation
    feats = {
        "amber_bond_atom_index": [torch.tensor([[0], [1]], dtype=torch.long)],  # (2, 1) shape
        "amber_bond_equilibrium": [torch.tensor([1.4], dtype=torch.float32)],
        "amber_bond_weights": [torch.tensor([1.0], dtype=torch.float32)]
    }

    # Parameters
    parameters = {
        "base_force_constant": 100.0,
        "guidance_weight": 1.0,
        "resampling_weight": 0.0
    }

    return coords, feats, parameters


def create_test_amber_angle_data():
    """Create test data for Amber angle potential validation."""
    print("Creating test data for Amber angle potential...")

    # Create test coordinates (3 atoms forming 1 angle)
    coords = torch.tensor([
        [[0.0, 0.0, 0.0],   # Atom 0
         [1.0, 0.0, 0.0],   # Atom 1 (center)
         [1.5, 1.0, 0.0]]   # Atom 2
    ], dtype=torch.float32).unsqueeze(0)  # Add batch dimension

    # Create features for 1 angle: 0-1-2
    # Based on the actual compute_args implementation
    feats = {
        "amber_angle_atom_index": [torch.tensor([[0], [1], [2]], dtype=torch.long)],  # (3, 1) shape
        "amber_angle_equilibrium_angle": [torch.tensor([2.0], dtype=torch.float32)],  # radians
        "amber_angle_equilibrium_distance": [torch.tensor([2.2], dtype=torch.float32)],  # 1-3 distance
        "amber_angle_force_constant": [torch.tensor([50.0], dtype=torch.float32)],
        "amber_angle_weights": [torch.tensor([1.0], dtype=torch.float32)]
    }

    # Parameters
    parameters = {
        "base_force_constant": 80.0,
        "guidance_weight": 1.0,
        "resampling_weight": 0.0
    }

    return coords, feats, parameters


def create_test_amber_dihedral_data():
    """Create test data for Amber dihedral potential validation."""
    print("Creating test data for Amber dihedral potential...")

    # Create test coordinates (4 atoms forming 1 dihedral)
    coords = torch.tensor([
        [[0.0, 0.0, 0.0],   # Atom 0
         [1.0, 0.0, 0.0],   # Atom 1
         [2.0, 1.0, 0.0],   # Atom 2
         [3.0, 1.0, 1.0]]   # Atom 3
    ], dtype=torch.float32).unsqueeze(0)  # Add batch dimension

    # Create features for 1 dihedral: 0-1-2-3
    # Based on the actual compute_args implementation
    feats = {
        "amber_dihedral_atom_index": [torch.tensor([[0], [1], [2], [3]], dtype=torch.long)],  # (4, 1) shape
        "amber_dihedral_force_constant": [torch.tensor([2.0], dtype=torch.float32)],
        "amber_dihedral_periodicity": [torch.tensor([2], dtype=torch.long)],
        "amber_dihedral_phase": [torch.tensor([0.0], dtype=torch.float32)],
        "amber_dihedral_weight": [torch.tensor([1.0], dtype=torch.float32)]
    }

    # Parameters
    parameters = {
        "base_force_constant": 10.0,
        "guidance_weight": 1.0,
        "resampling_weight": 0.0
    }

    return coords, feats, parameters


def test_amber_bond_gradient_validation():
    """Test AmberBondPotential gradient validation."""
    print("\n" + "="*60)
    print("Testing AmberBondPotential Gradient Validation")
    print("="*60)
    
    # Create potential with debug enabled
    potential = AmberBondPotential(debug_enabled=True)
    
    # Get test data
    coords, feats, parameters = create_test_amber_bond_data()
    
    # Test basic functionality first
    print("\n1. Testing basic compute functionality...")
    energy = potential.compute(coords, feats, parameters)
    print(f"   Energy computed: {energy.item():.6f}")
    
    print("\n2. Testing analytic gradient...")
    analytic_grad = potential.compute_gradient(coords, feats, parameters)
    print(f"   Analytic gradient norm: {torch.norm(analytic_grad).item():.6f}")
    
    print("\n3. Testing numerical gradient...")
    numerical_grad = potential.compute_numerical_gradient(coords, feats, parameters, eps=1e-5)
    print(f"   Numerical gradient norm: {torch.norm(numerical_grad).item():.6f}")
    
    print("\n4. Running gradient validation...")
    validation_results = potential.validate_amber_gradients(coords, feats, parameters)
    
    print(f"\n5. Validation Results:")
    print(f"   Valid: {validation_results['is_valid']}")
    print(f"   Max absolute error: {validation_results['max_abs_error']:.2e}")
    print(f"   Max relative error: {validation_results['max_rel_error']:.2e}")
    print(f"   Constraint count: {validation_results['constraint_count']}")
    
    return validation_results['is_valid']


def test_amber_angle_gradient_validation():
    """Test AmberAnglePotential gradient validation."""
    print("\n" + "="*60)
    print("Testing AmberAnglePotential Gradient Validation")
    print("="*60)
    
    # Create potential with debug enabled
    potential = AmberAnglePotential(debug_enabled=True)
    
    # Get test data
    coords, feats, parameters = create_test_amber_angle_data()
    
    # Test basic functionality first
    print("\n1. Testing basic compute functionality...")
    energy = potential.compute(coords, feats, parameters)
    print(f"   Energy computed: {energy.item():.6f}")
    
    print("\n2. Testing analytic gradient...")
    analytic_grad = potential.compute_gradient(coords, feats, parameters)
    print(f"   Analytic gradient norm: {torch.norm(analytic_grad).item():.6f}")
    
    print("\n3. Testing numerical gradient...")
    numerical_grad = potential.compute_numerical_gradient(coords, feats, parameters, eps=1e-5)
    print(f"   Numerical gradient norm: {torch.norm(numerical_grad).item():.6f}")
    
    print("\n4. Running gradient validation...")
    validation_results = potential.validate_amber_gradients(coords, feats, parameters)
    
    print(f"\n5. Validation Results:")
    print(f"   Valid: {validation_results['is_valid']}")
    print(f"   Max absolute error: {validation_results['max_abs_error']:.2e}")
    print(f"   Max relative error: {validation_results['max_rel_error']:.2e}")
    print(f"   Constraint count: {validation_results['constraint_count']}")
    
    return validation_results['is_valid']


def test_amber_dihedral_gradient_validation():
    """Test AmberDihedralPotential gradient validation."""
    print("\n" + "="*60)
    print("Testing AmberDihedralPotential Gradient Validation")
    print("="*60)
    
    # Create potential with debug enabled
    potential = AmberDihedralPotential(debug_enabled=True)
    
    # Get test data
    coords, feats, parameters = create_test_amber_dihedral_data()
    
    # Test basic functionality first
    print("\n1. Testing basic compute functionality...")
    energy = potential.compute(coords, feats, parameters)
    print(f"   Energy computed: {energy.item():.6f}")
    
    print("\n2. Testing analytic gradient...")
    analytic_grad = potential.compute_gradient(coords, feats, parameters)
    print(f"   Analytic gradient norm: {torch.norm(analytic_grad).item():.6f}")
    
    print("\n3. Testing numerical gradient...")
    numerical_grad = potential.compute_numerical_gradient(coords, feats, parameters, eps=1e-5)
    print(f"   Numerical gradient norm: {torch.norm(numerical_grad).item():.6f}")
    
    print("\n4. Running gradient validation...")
    validation_results = potential.validate_amber_gradients(coords, feats, parameters)
    
    print(f"\n5. Validation Results:")
    print(f"   Valid: {validation_results['is_valid']}")
    print(f"   Max absolute error: {validation_results['max_abs_error']:.2e}")
    print(f"   Max relative error: {validation_results['max_rel_error']:.2e}")
    print(f"   Constraint count: {validation_results['constraint_count']}")
    
    return validation_results['is_valid']


def main():
    """Main test function."""
    print("Amber Force Field Gradient Validation Test")
    print("=" * 60)
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    np.random.seed(42)
    
    # Run tests
    results = {}
    
    try:
        results['bond'] = test_amber_bond_gradient_validation()
    except Exception as e:
        print(f"AmberBondPotential test failed: {e}")
        results['bond'] = False
    
    try:
        results['angle'] = test_amber_angle_gradient_validation()
    except Exception as e:
        print(f"AmberAnglePotential test failed: {e}")
        results['angle'] = False
    
    try:
        results['dihedral'] = test_amber_dihedral_gradient_validation()
    except Exception as e:
        print(f"AmberDihedralPotential test failed: {e}")
        results['dihedral'] = False
    
    # Summary
    print("\n" + "="*60)
    print("FINAL RESULTS")
    print("="*60)
    print(f"AmberBondPotential:     {'PASSED' if results['bond'] else 'FAILED'}")
    print(f"AmberAnglePotential:    {'PASSED' if results['angle'] else 'FAILED'}")
    print(f"AmberDihedralPotential: {'PASSED' if results['dihedral'] else 'FAILED'}")
    
    all_passed = all(results.values())
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
